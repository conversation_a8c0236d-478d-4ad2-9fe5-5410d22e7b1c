"use client"

import { type Node, type Edge } from "@xyflow/react"

export interface LayoutOptions {
  horizontalSpacing?: number
  verticalSpacing?: number
  startX?: number
  startY?: number
  alignByLayer?: boolean
  handleOrientation?: 'horizontal' | 'vertical' | 'auto'
  animationDuration?: number
  onComplete?: (finalPositions: Map<string, { x: number; y: number }>) => void
}

export interface AutoLayoutResult {
  positions: Map<string, { x: number; y: number }>
  orientation: 'horizontal' | 'vertical'
}

/**
 * Detects the predominant handle orientation in the workflow
 */
export const detectHandleOrientation = (nodes: Node[]): 'horizontal' | 'vertical' => {
  if (nodes.length === 0) return 'horizontal'
  
  // For now, default to horizontal layout
  // This can be enhanced to detect based on node connections
  return 'horizontal'
}

/**
 * Calculates auto-layout positions for nodes with improved spacing and alignment
 */
export const calculateAutoLayout = (
  nodes: Node[],
  edges: Edge[],
  options: LayoutOptions = {}
): AutoLayoutResult => {
  const {
    horizontalSpacing = 300,
    verticalSpacing = 200,
    startX = 100,
    startY = 100,
    alignByLayer = true,
    handleOrientation = 'auto',
  } = options

  if (nodes.length === 0) {
    return { positions: new Map(), orientation: 'horizontal' }
  }

  const orientation = handleOrientation === 'auto' 
    ? detectHandleOrientation(nodes) 
    : handleOrientation

  // Create adjacency list and calculate in-degrees
  const adjacencyList = new Map<string, string[]>()
  const inDegree = new Map<string, number>()
  
  // Initialize
  nodes.forEach(node => {
    adjacencyList.set(node.id, [])
    inDegree.set(node.id, 0)
  })

  // Build graph
  edges.forEach(edge => {
    if (edge.source && edge.target) {
      adjacencyList.get(edge.source)?.push(edge.target)
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1)
    }
  })

  // Topological sort to determine levels
  const levels = new Map<string, number>()
  const levelNodes = new Map<number, string[]>()
  const queue: string[] = []

  // Find root nodes (nodes with no incoming edges)
  nodes.forEach(node => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id)
      levels.set(node.id, 0)
    }
  })

  // If no root nodes, use the first node as root
  if (queue.length === 0 && nodes.length > 0) {
    const firstNode = nodes[0]
    queue.push(firstNode.id)
    levels.set(firstNode.id, 0)
  }

  // BFS to assign levels
  while (queue.length > 0) {
    const nodeId = queue.shift()!
    const currentLevel = levels.get(nodeId) || 0
    
    // Add to level tracking
    if (!levelNodes.has(currentLevel)) {
      levelNodes.set(currentLevel, [])
    }
    levelNodes.get(currentLevel)!.push(nodeId)

    // Process children
    const children = adjacencyList.get(nodeId) || []
    children.forEach(childId => {
      const childLevel = Math.max(levels.get(childId) || 0, currentLevel + 1)
      levels.set(childId, childLevel)
      
      // Decrease in-degree and add to queue if ready
      const newInDegree = (inDegree.get(childId) || 0) - 1
      inDegree.set(childId, newInDegree)
      
      if (newInDegree === 0) {
        queue.push(childId)
      }
    })
  }

  // Handle any remaining nodes (cycles or disconnected components)
  nodes.forEach(node => {
    if (!levels.has(node.id)) {
      const maxLevel = Math.max(...Array.from(levels.values()), -1)
      levels.set(node.id, maxLevel + 1)
      
      const level = maxLevel + 1
      if (!levelNodes.has(level)) {
        levelNodes.set(level, [])
      }
      levelNodes.get(level)!.push(node.id)
    }
  })

  // Calculate positions based on orientation
  const positions = new Map<string, { x: number; y: number }>()

  if (orientation === 'horizontal') {
    // Horizontal layout (left to right)
    levelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length
      const levelHeight = (totalNodesInLevel - 1) * verticalSpacing
      const startYForLevel = startY - levelHeight / 2

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startX + level * horizontalSpacing,
          y: startYForLevel + indexInLevel * verticalSpacing
        })
      })
    })
  } else {
    // Vertical layout (top to bottom)
    levelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length
      const levelWidth = (totalNodesInLevel - 1) * horizontalSpacing
      const startXForLevel = startX - levelWidth / 2

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startXForLevel + indexInLevel * horizontalSpacing,
          y: startY + level * verticalSpacing
        })
      })
    })
  }

  return { positions, orientation }
}

/**
 * Easing function for smooth animations
 */
export const easeOutCubic = (t: number): number => 1 - (1 - t) ** 3

/**
 * Enhanced auto-layout function with smooth animations
 */
export const applyAutoLayoutSmooth = (
  nodes: Node[],
  edges: Edge[],
  updateNodes: (nodes: Node[]) => void,
  fitView?: (options?: { padding?: number; duration?: number }) => void,
  options: LayoutOptions = {}
): void => {
  const {
    animationDuration = 500,
    onComplete,
    ...layoutOptions
  } = options

  if (nodes.length === 0) return

  const { positions: targetPositions, orientation } = calculateAutoLayout(nodes, edges, layoutOptions)

  if (targetPositions.size === 0) return

  // Store initial positions
  const initialPositions = new Map<string, { x: number; y: number }>()
  nodes.forEach(node => {
    initialPositions.set(node.id, { x: node.position.x, y: node.position.y })
  })

  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / animationDuration, 1)
    const easedProgress = easeOutCubic(progress)

    // Update node positions
    const updatedNodes = nodes.map(node => {
      const initialPos = initialPositions.get(node.id)
      const targetPos = targetPositions.get(node.id)
      
      if (!initialPos || !targetPos) return node

      const newPosition = {
        x: initialPos.x + (targetPos.x - initialPos.x) * easedProgress,
        y: initialPos.y + (targetPos.y - initialPos.y) * easedProgress,
      }

      return {
        ...node,
        position: newPosition
      }
    })

    updateNodes(updatedNodes)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      // Animation complete
      if (fitView) {
        fitView({
          padding: 0.2,
          duration: 400,
        })
      }

      if (onComplete) {
        onComplete(targetPositions)
      }
    }
  }

  animate()
}

/**
 * Debounced auto layout to prevent rapid triggering
 */
export const createDebouncedAutoLayout = (
  autoLayoutFn: () => void,
  delay: number = 250
) => {
  let timeoutId: NodeJS.Timeout | null = null

  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      autoLayoutFn()
      timeoutId = null
    }, delay)

    // Return cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }
  }
}
